import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class WasabiService {
  private s3: AWS.S3;
  private bucketName: string;

  constructor(private configService: ConfigService) {
    // Get credentials from config service
    const accessKeyId =
      this.configService.get('WASABI_ACCESS_KEY') || 'OQL1BX7MOF71KL0MM0UM';
    const secretAccessKey =
      this.configService.get('WASABI_SECRET_KEY') ||
      '07e80kBbTY2T9kPr1E43kMiq1PkO6lw9LUDhKeMi';

    console.log('🔧 Wasabi Config:', {
      accessKeyId: accessKeyId
        ? `${accessKeyId.substring(0, 4)}...`
        : 'NOT_SET',
      secretAccessKey: secretAccessKey
        ? `${secretAccessKey.substring(0, 4)}...`
        : 'NOT_SET',
      endpoint: 'https://s3.ap-southeast-2.wasabisys.com',
    });

    // Wasabi S3-compatible configuration
    this.s3 = new AWS.S3({
      endpoint: 'https://s3.ap-southeast-2.wasabisys.com',
      accessKeyId,
      secretAccessKey,
      region: 'ap-southeast-2',
      s3ForcePathStyle: true,
      signatureVersion: 'v4',
    });

    this.bucketName =
      this.configService.get('WASABI_BUCKET_NAME') || 'airsmart';
  }

  /**
   * Upload 3D model file to Wasabi
   * @param file - The uploaded file
   * @param moduleId - Module ID for organizing files
   * @returns Promise with upload result and public URL
   */
  async upload3DModel(
    file: Express.Multer.File,
    moduleId: string,
  ): Promise<{
    url: string;
    key: string;
    size: number;
    contentType: string;
  }> {
    try {
      // Generate unique filename
      const timestamp = Date.now();
      const fileExtension = file.originalname.split('.').pop();
      const key = `3d-models/${moduleId}/${timestamp}.${fileExtension}`;

      // Upload parameters
      const uploadParams: AWS.S3.PutObjectRequest = {
        Bucket: this.bucketName,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
        ACL: 'public-read', // Make file publicly accessible
        Metadata: {
          'original-name': file.originalname,
          'module-id': moduleId,
          'uploaded-at': new Date().toISOString(),
        },
      };

      console.log(`🔄 Uploading 3D model to Wasabi: ${key}`);

      // Perform upload
      const result = await this.s3.upload(uploadParams).promise();

      console.log(`✅ 3D model uploaded successfully: ${result.Location}`);

      return {
        url: result.Location,
        key: result.Key,
        size: file.size,
        contentType: file.mimetype,
      };
    } catch (error) {
      console.error('❌ Error uploading 3D model to Wasabi:', error);
      throw new Error(`Failed to upload 3D model: ${error.message}`);
    }
  }

  /**
   * Delete 3D model from Wasabi
   * @param key - The file key to delete
   * @returns Promise with deletion result
   */
  async delete3DModel(key: string): Promise<void> {
    try {
      const deleteParams: AWS.S3.DeleteObjectRequest = {
        Bucket: this.bucketName,
        Key: key,
      };

      console.log(`🔄 Deleting 3D model from Wasabi: ${key}`);

      await this.s3.deleteObject(deleteParams).promise();

      console.log(`✅ 3D model deleted successfully: ${key}`);
    } catch (error) {
      console.error('❌ Error deleting 3D model from Wasabi:', error);
      throw new Error(`Failed to delete 3D model: ${error.message}`);
    }
  }

  /**
   * Generate signed URL for private access (if needed)
   * @param key - The file key
   * @param expiresIn - Expiration time in seconds (default: 1 hour)
   * @returns Signed URL
   */
  async getSignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const params = {
        Bucket: this.bucketName,
        Key: key,
        Expires: expiresIn,
      };

      const signedUrl = await this.s3.getSignedUrlPromise('getObject', params);
      return signedUrl;
    } catch (error) {
      console.error('❌ Error generating signed URL:', error);
      throw new Error(`Failed to generate signed URL: ${error.message}`);
    }
  }

  /**
   * List all 3D models for a module
   * @param moduleId - Module ID
   * @returns Array of model objects
   */
  async list3DModels(moduleId: string): Promise<
    Array<{
      key: string;
      url: string;
      size: number;
      lastModified: Date;
      metadata?: any;
    }>
  > {
    try {
      const listParams: AWS.S3.ListObjectsV2Request = {
        Bucket: this.bucketName,
        Prefix: `3d-models/${moduleId}/`,
      };

      const result = await this.s3.listObjectsV2(listParams).promise();

      if (!result.Contents) {
        return [];
      }

      return result.Contents.map((object) => ({
        key: object.Key || '',
        url: `https://${this.bucketName}.s3.ap-southeast-2.wasabisys.com/${object.Key}`,
        size: object.Size || 0,
        lastModified: object.LastModified || new Date(),
      }));
    } catch (error) {
      console.error('❌ Error listing 3D models:', error);
      throw new Error(`Failed to list 3D models: ${error.message}`);
    }
  }

  /**
   * Check if file is a valid 3D model format
   * @param file - The uploaded file
   * @returns boolean
   */
  isValid3DModel(file: Express.Multer.File): boolean {
    const validExtensions = ['.obj', '.gltf', '.glb', '.fbx', '.dae', '.3ds'];
    const validMimeTypes = [
      'application/octet-stream',
      'model/obj',
      'model/gltf+json',
      'model/gltf-binary',
      'application/json', // for .gltf files
    ];

    const fileExtension =
      '.' + file.originalname.split('.').pop()?.toLowerCase();

    return (
      validExtensions.includes(fileExtension) ||
      validMimeTypes.includes(file.mimetype)
    );
  }

  /**
   * Get file info without downloading
   * @param key - The file key
   * @returns File metadata
   */
  async getFileInfo(key: string): Promise<{
    size: number;
    contentType: string;
    lastModified: Date;
    metadata?: any;
  }> {
    try {
      const params = {
        Bucket: this.bucketName,
        Key: key,
      };

      const result = await this.s3.headObject(params).promise();

      return {
        size: result.ContentLength || 0,
        contentType: result.ContentType || 'application/octet-stream',
        lastModified: result.LastModified || new Date(),
        metadata: result.Metadata,
      };
    } catch (error) {
      console.error('❌ Error getting file info:', error);
      throw new Error(`Failed to get file info: ${error.message}`);
    }
  }

  /**
   * Upload image file to Wasabi
   * @param file - The uploaded image file
   * @param moduleId - Module ID for organizing files
   * @returns Promise with upload result and public URL
   */
  async uploadImage(
    file: Express.Multer.File,
    moduleId: string,
  ): Promise<{
    url: string;
    key: string;
    size: number;
    contentType: string;
  }> {
    try {
      // Generate unique filename
      const timestamp = Date.now();
      const fileExtension = file.originalname.split('.').pop();
      const key = `images/${moduleId}/${timestamp}.${fileExtension}`;

      // Upload parameters
      const uploadParams: AWS.S3.PutObjectRequest = {
        Bucket: this.bucketName,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
        ACL: 'public-read', // Make file publicly accessible
        Metadata: {
          'original-name': file.originalname,
          'module-id': moduleId,
          'uploaded-at': new Date().toISOString(),
          'file-type': 'image',
        },
      };

      console.log(`🔄 Uploading image to Wasabi: ${key}`);

      // Perform upload
      const result = await this.s3.upload(uploadParams).promise();

      console.log(`✅ Image uploaded successfully: ${result.Location}`);

      return {
        url: result.Location,
        key: result.Key,
        size: file.size,
        contentType: file.mimetype,
      };
    } catch (error) {
      console.error('❌ Error uploading image to Wasabi:', error);
      throw new Error(`Failed to upload image: ${error.message}`);
    }
  }

  /**
   * Delete image from Wasabi
   * @param key - The file key to delete
   * @returns Promise with deletion result
   */
  async deleteImage(key: string): Promise<void> {
    try {
      const deleteParams = {
        Bucket: this.bucketName,
        Key: key,
      };

      console.log(`🔄 Deleting image from Wasabi: ${key}`);
      await this.s3.deleteObject(deleteParams).promise();
      console.log(`✅ Image deleted successfully: ${key}`);
    } catch (error) {
      console.error('❌ Error deleting image from Wasabi:', error);
      throw new Error(`Failed to delete image: ${error.message}`);
    }
  }

  /**
   * Check if file is a valid image format
   * @param file - The uploaded file
   * @returns boolean
   */
  isValidImageFile(file: Express.Multer.File): boolean {
    const validExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.webp',
      '.bmp',
      '.tiff',
    ];
    const validMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/bmp',
      'image/tiff',
    ];

    const fileExtension =
      '.' + file.originalname.split('.').pop()?.toLowerCase();

    return (
      validExtensions.includes(fileExtension) &&
      validMimeTypes.includes(file.mimetype)
    );
  }

  /**
   * Upload video file to Wasabi
   * @param file - The uploaded video file
   * @param moduleId - Module ID for organizing files
   * @returns Promise with upload result and public URL
   */
  async uploadVideo(
    file: Express.Multer.File,
    moduleId: string,
  ): Promise<{
    url: string;
    key: string;
    size: number;
    contentType: string;
  }> {
    try {
      // Generate unique filename
      const timestamp = Date.now();
      const fileExtension = file.originalname.split('.').pop();
      const key = `videos/${moduleId}/${timestamp}.${fileExtension}`;

      // Upload parameters
      const uploadParams: AWS.S3.PutObjectRequest = {
        Bucket: this.bucketName,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
        ACL: 'public-read', // Make file publicly accessible
        Metadata: {
          'original-name': file.originalname,
          'module-id': moduleId,
          'uploaded-at': new Date().toISOString(),
          'file-type': 'video',
        },
      };

      console.log(`🔄 Uploading video to Wasabi: ${key}`);

      // Perform upload
      const result = await this.s3.upload(uploadParams).promise();

      console.log(`✅ Video uploaded successfully: ${result.Location}`);

      return {
        url: result.Location,
        key: result.Key,
        size: file.size,
        contentType: file.mimetype,
      };
    } catch (error) {
      console.error('❌ Error uploading video to Wasabi:', error);
      throw new Error(`Failed to upload video: ${error.message}`);
    }
  }

  /**
   * Delete video from Wasabi
   * @param key - The file key to delete
   * @returns Promise with deletion result
   */
  async deleteVideo(key: string): Promise<void> {
    try {
      const deleteParams = {
        Bucket: this.bucketName,
        Key: key,
      };

      console.log(`🔄 Deleting video from Wasabi: ${key}`);
      await this.s3.deleteObject(deleteParams).promise();
      console.log(`✅ Video deleted successfully: ${key}`);
    } catch (error) {
      console.error('❌ Error deleting video from Wasabi:', error);
      throw new Error(`Failed to delete video: ${error.message}`);
    }
  }

  /**
   * Check if file is a valid video format
   * @param file - The uploaded file
   * @returns boolean
   */
  isValidVideoFile(file: Express.Multer.File): boolean {
    const validExtensions = [
      '.mp4',
      '.webm',
      '.ogg',
      '.avi',
      '.mov',
      '.wmv',
      '.flv',
      '.mkv',
    ];
    const validMimeTypes = [
      'video/mp4',
      'video/webm',
      'video/ogg',
      'video/avi',
      'video/quicktime', // .mov files
      'video/x-msvideo', // .avi files
      'video/x-ms-wmv', // .wmv files
      'video/x-flv', // .flv files
      'video/x-matroska', // .mkv files
    ];

    const fileExtension =
      '.' + file.originalname.split('.').pop()?.toLowerCase();

    return (
      validExtensions.includes(fileExtension) &&
      validMimeTypes.includes(file.mimetype)
    );
  }

  /**
   * Extract file key from Wasabi URL
   * @param url - The Wasabi URL
   * @returns File key or null if not a Wasabi URL
   */
  extractKeyFromUrl(url: string): string | null {
    if (!url) return null;

    try {
      // Check if it's a Wasabi URL
      if (!url.includes('wasabisys.com')) {
        return null;
      }

      console.log('🔍 Extracting key from URL:', url);

      // Extract key from URL patterns like:
      // https://s3.ap-southeast-2.wasabisys.com/airsmart/images/moduleId/timestamp.ext
      // https://airsmart.s3.ap-southeast-2.wasabisys.com/images/moduleId/timestamp.ext

      // Pattern 1: https://s3.region.wasabisys.com/bucket/path/to/file
      const pathStyleMatch = url.match(/wasabisys\.com\/[^\/]+\/(.+)$/);
      if (pathStyleMatch) {
        const key = pathStyleMatch[1];
        console.log('🔍 Path-style URL detected, key:', key);
        return key;
      }

      // Pattern 2: https://bucket.s3.region.wasabisys.com/path/to/file
      const subdomainMatch = url.match(
        /^https:\/\/[^\/]+\.wasabisys\.com\/(.+)$/,
      );
      if (subdomainMatch) {
        const key = subdomainMatch[1];
        console.log('🔍 Subdomain-style URL detected, key:', key);
        return key;
      }

      // Pattern 3: Generic fallback - everything after wasabisys.com/
      const genericMatch = url.match(/wasabisys\.com\/(.+)$/);
      if (genericMatch) {
        const fullPath = genericMatch[1];
        let key: string;

        // If it starts with bucket name, remove it
        if (fullPath.startsWith(`${this.bucketName}/`)) {
          key = fullPath.substring(this.bucketName.length + 1);
        } else {
          key = fullPath;
        }
        console.log('🔍 Generic pattern detected, key:', key);
        return key;
      }

      console.log('⚠️ Could not extract key from URL:', url);
      return null;
    } catch (error) {
      console.error('❌ Error extracting key from URL:', error);
      return null;
    }
  }

  /**
   * Delete file from Wasabi by URL
   * @param url - The file URL to delete
   * @returns Promise with deletion result
   */
  async deleteFileByUrl(url: string): Promise<boolean> {
    const key = this.extractKeyFromUrl(url);
    if (!key) {
      console.log(
        `⚠️ Skipping deletion - could not extract key from URL: ${url}`,
      );
      return false;
    }

    try {
      const deleteParams = {
        Bucket: this.bucketName,
        Key: key,
      };

      console.log(
        `🔄 Deleting file from Wasabi - Bucket: ${this.bucketName}, Key: ${key}`,
      );
      const result = await this.s3.deleteObject(deleteParams).promise();
      console.log(`✅ File deleted successfully: ${key}`, result);
      return true;
    } catch (error) {
      console.error('❌ Error deleting file from Wasabi:', error);
      console.error('❌ Delete params were:', { bucket: this.bucketName, key });
      // Don't throw error to avoid breaking the main operation
      return false;
    }
  }
}
