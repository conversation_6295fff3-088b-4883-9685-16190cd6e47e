import { IsString, <PERSON>Optional, <PERSON><PERSON><PERSON>ber, Is<PERSON><PERSON>y, IsObject, IsBoolean, Min } from 'class-validator';

// DTO for Image Annotation
export class ImageAnnotationDto {
  @IsString()
  id: string;

  @IsObject()
  position: {
    x: number; // Percentage position (0-100)
    y: number; // Percentage position (0-100)
  };

  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  action?: string; // 'info', 'highlight', 'popup', etc.

  @IsOptional()
  @IsObject()
  style?: {
    color?: string; // 'primary', 'secondary', 'error', etc.
    size?: string; // 'small', 'medium', 'large'
  };
}

// DTO for Video Annotation
export class VideoAnnotationDto {
  @IsString()
  id: string;

  @IsObject()
  position: {
    x: number; // Percentage position (0-100)
    y: number; // Percentage position (0-100)
  };

  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNumber()
  startTime: number; // Start time in seconds

  @IsNumber()
  endTime: number; // End time in seconds

  @IsOptional()
  @IsString()
  action?: string; // 'info', 'highlight', 'popup', etc.

  @IsOptional()
  @IsObject()
  style?: {
    color?: string; // 'primary', 'secondary', 'error', etc.
    size?: string; // 'small', 'medium', 'large'
  };
}

// Simplified Step DTO to match database structure - actions and introjsSteps removed
export class CreateStepDto {
  @IsOptional()
  @IsNumber()
  @Min(1)
  order?: number; // Will be auto-calculated if not provided

  @IsString()
  title: string;

  @IsString()
  content: string;

  @IsOptional()
  @IsObject()
  camera?: {
    position: number[]; // [x, y, z]
    target: number[]; // [x, y, z]
    fov?: number;
    zoom?: number;
  };

  @IsOptional()
  @IsArray()
  hotspots?: Array<{
    id: string;
    position: number[]; // [x, y, z]
    label: string;
    action: string;
    description?: string;
    color?: string;
    icon?: string;
    data?: any;
  }>;

  @IsOptional()
  @IsBoolean()
  autoPlay?: boolean;

  @IsOptional()
  @IsNumber()
  duration?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsObject()
  metadata?: any;

  @IsOptional()
  @IsString()
  imageUrl?: string; // Add imageUrl field for image steps

  @IsOptional()
  @IsString()
  videoUrl?: string; // Add videoUrl field for video steps

  @IsOptional()
  @IsString()
  stepType?: string; // Type of step: '3d-model', 'video', 'image'

  // Add imageAnnotations field for image steps
  @IsOptional()
  @IsArray()
  imageAnnotations?: ImageAnnotationDto[];

  // Add videoAnnotations field for video steps
  @IsOptional()
  @IsArray()
  videoAnnotations?: VideoAnnotationDto[];
}