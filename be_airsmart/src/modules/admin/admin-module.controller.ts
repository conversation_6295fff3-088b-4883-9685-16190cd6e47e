/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpStatus,
  HttpException,
  ValidationPipe,
  UsePipes,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ModuleService } from '../module/module.service';
import { WasabiService } from '../../common/wasabi/wasabi.service';
import { CreateModuleDto } from '../module/dto/create-module.dto';
import { UpdateModuleDto } from '../module/dto/update-module.dto';
import { CreateCourseDto } from '../module/dto/create-course.dto';
import { UpdateCourseDto } from '../module/dto/update-course.dto';
import { CreateStepDto } from '../module/dto/create-step.dto';
import { UpdateStepDto } from '../module/dto/update-step.dto';
import { CreateQuizQuestionDto } from '../module/dto/create-quiz.dto';
import { UpdateQuizQuestionDto } from '../module/dto/update-quiz.dto';

@Controller('admin')
// @UsePipes(new ValidationPipe({ transform: true }))
export class AdminModuleController {
  constructor(
    private readonly moduleService: ModuleService,
    private readonly wasabiService: WasabiService,
  ) {}

  // ========================================
  // ADMIN MODULE ENDPOINTS
  // ========================================

  /**
   * [ADMIN] Create a new module
   * POST /admin/modules
   */
  @Post('modules')
  async createModule(@Body() createModuleDto: CreateModuleDto) {
    try {
      const result =
        await this.moduleService.createModuleAdmin(createModuleDto);
      return {
        success: true,
        message: 'Module created successfully',
        data: result,
      };
    } catch (error: any) {
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to create module',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * [ADMIN] Create a new module with 3D model upload
   * POST /admin/modules/with-3d-model
   */
  @Post('modules/with-3d-model')
  @UseInterceptors(FileInterceptor('model'))
  async createModuleWith3DModel(
    @UploadedFile() file: Express.Multer.File,
    @Body('moduleData') moduleDataString: string,
  ) {
    try {
      console.log('🔄 Admin creating module with 3D model...');

      // Validate file
      if (!file) {
        throw new BadRequestException('3D model file is required');
      }

      // Validate file type
      if (!this.wasabiService.isValid3DModel(file)) {
        throw new BadRequestException(
          'Invalid 3D model file format. Supported: .obj, .gltf, .glb',
        );
      }

      // Parse module data
      let moduleData: CreateModuleDto;
      try {
        moduleData = JSON.parse(moduleDataString);
      } catch (error) {
        throw new BadRequestException('Invalid module data format');
      }

      // Validate module data
      if (!moduleData.name || !moduleData.description) {
        throw new BadRequestException(
          'Module name and description are required',
        );
      }

      console.log('📁 File info:', {
        name: file.originalname,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        type: file.mimetype,
      });

      // Create module first to get ID
      const createdModule =
        await this.moduleService.createModuleAdmin(moduleData);
      console.log('✅ Module created with ID:', createdModule.id);

      try {
        // Upload 3D model to Wasabi
        console.log('🔄 Uploading 3D model to Wasabi...');
        const uploadResult = await this.wasabiService.upload3DModel(
          file,
          createdModule.id,
        );
        console.log('✅ 3D model uploaded:', uploadResult.url);

        // Update module with model URL
        const updatedModule = await this.moduleService.updateModuleAdmin(
          createdModule.id,
          {
            modelUrl: uploadResult.url,
            modelType: moduleData.modelType || 'auto',
          },
        );

        console.log('✅ Module updated with 3D model URL');

        return {
          success: true,
          message: 'Module created successfully with 3D model',
          data: {
            ...updatedModule,
            modelInfo: {
              url: uploadResult.url,
              key: uploadResult.key,
              size: uploadResult.size,
              contentType: uploadResult.contentType,
            },
          },
        };
      } catch (uploadError) {
        // If upload fails, delete the created module
        console.error('❌ Upload failed, cleaning up module:', uploadError);
        await this.moduleService.deleteModuleAdmin(createdModule.id);
        throw new BadRequestException(
          `Failed to upload 3D model: ${uploadError.message}`,
        );
      }
    } catch (error) {
      console.error('❌ Error creating module with 3D model:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to create module with 3D model',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * [ADMIN] Get all modules with admin metadata
   * GET /admin/modules
   */
  @Get('modules')
  async getAllModulesAdmin() {
    try {
      const modules = await this.moduleService.getModulesAdmin();
      return {
        success: true,
        message: 'Modules retrieved successfully',
        data: modules,
        count: modules.length,
      };
    } catch (error: any) {
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to get modules',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * [ADMIN] Get single module by ID
   * GET /admin/modules/:id
   */
  @Get('modules/:id')
  async getModuleAdmin(@Param('id') id: string) {
    try {
      const module = await this.moduleService.getModuleAdmin(id);
      if (!module) {
        throw new HttpException(
          {
            success: false,
            message: 'Module not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        success: true,
        message: 'Module retrieved successfully',
        data: module,
      };
    } catch (error: any) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to get module',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * [ADMIN] Update a module
   * PATCH /admin/modules/:id
   */
  @Patch('modules/:id')
  async updateModule(
    @Param('id') id: string,
    @Body() updateModuleDto: UpdateModuleDto,
  ) {
    try {
      const result = await this.moduleService.updateModuleAdmin(
        id,
        updateModuleDto,
      );
      return {
        success: true,
        message: 'Module updated successfully',
        data: result,
      };
    } catch (error: any) {
      const statusCode = error?.message?.includes('not found')
        ? HttpStatus.NOT_FOUND
        : HttpStatus.BAD_REQUEST;

      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to update module',
          error: error?.message || 'Unknown error',
        },
        statusCode,
      );
    }
  }

  /**
   * [ADMIN] Delete a module
   * DELETE /admin/modules/:id
   */
  @Delete('modules/:id')
  async deleteModule(@Param('id') id: string) {
    try {
      console.log('🚀 DELETE MODULE CALLED (admin route):', { moduleId: id });

      const result = await this.moduleService.deleteModuleAdmin(id);

      console.log(
        '🔍 DELETE MODULE RESULT (admin route):',
        JSON.stringify(result, null, 2),
      );

      // Clean up files from Wasabi if they exist
      const cleanupPromises: Promise<boolean>[] = [];

      console.log('🔍 Module data for cleanup:', {
        modelUrl: result.moduleData?.modelUrl,
        stepsWithFiles: result.stepsData?.filter(
          (step) => step.imageUrl || step.videoUrl,
        ).length,
        totalFilesToCleanup: result.filesToCleanup,
      });

      // Cleanup module's 3D model
      if (result.moduleData?.modelUrl) {
        console.log(
          '🗑️ Cleaning up module 3D model:',
          result.moduleData.modelUrl,
        );
        cleanupPromises.push(
          this.wasabiService.deleteFileByUrl(result.moduleData.modelUrl),
        );
      }

      // Cleanup all step files (images and videos)
      if (result.stepsData) {
        result.stepsData.forEach((stepData, index) => {
          if (stepData?.imageUrl) {
            console.log(
              `🗑️ Cleaning up step ${index + 1} image:`,
              stepData.imageUrl,
            );
            cleanupPromises.push(
              this.wasabiService.deleteFileByUrl(stepData.imageUrl),
            );
          }
          if (stepData?.videoUrl) {
            console.log(
              `🗑️ Cleaning up step ${index + 1} video:`,
              stepData.videoUrl,
            );
            cleanupPromises.push(
              this.wasabiService.deleteFileByUrl(stepData.videoUrl),
            );
          }
        });
      }

      // Execute cleanup in parallel (non-blocking)
      if (cleanupPromises.length > 0) {
        console.log(
          `🔄 Starting cleanup of ${cleanupPromises.length} files...`,
        );
        Promise.all(cleanupPromises)
          .then((results) => {
            const successCount = results.filter(Boolean).length;
            console.log(
              `✅ Module files cleanup completed: ${successCount}/${results.length} files deleted successfully`,
            );
          })
          .catch((error) => {
            console.error('⚠️ Error during module files cleanup:', error);
            // Don't fail the main operation if cleanup fails
          });
      } else {
        console.log('ℹ️ No files to cleanup for this module');
      }

      return {
        success: true,
        message: 'Module deleted successfully',
        data: {
          id: result.id,
          deletedSteps: result.deletedSteps,
          deletedQuizQuestions: result.deletedQuizQuestions,
          filesCleanedUp: cleanupPromises.length,
        },
      };
    } catch (error: any) {
      const statusCode = error?.message?.includes('not found')
        ? HttpStatus.NOT_FOUND
        : error?.message?.includes('user progress')
          ? HttpStatus.CONFLICT
          : HttpStatus.BAD_REQUEST;

      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to delete module',
          error: error?.message || 'Unknown error',
        },
        statusCode,
      );
    }
  }

  /**
   * [ADMIN] Add 3D model to existing module
   * PATCH /admin/modules/:id/add-3d-model
   */
  @Patch('modules/:id/add-3d-model')
  @UseInterceptors(FileInterceptor('model'))
  async add3DModelToModule(
    @Param('id') moduleId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('modelType') modelType?: string,
  ) {
    try {
      console.log('🔄 Admin adding 3D model to existing module:', moduleId);

      // Validate file
      if (!file) {
        throw new BadRequestException('3D model file is required');
      }

      // Validate file type
      if (!this.wasabiService.isValid3DModel(file)) {
        throw new BadRequestException(
          'Invalid 3D model file format. Supported: .obj, .gltf, .glb',
        );
      }

      // Check if module exists
      const existingModule = await this.moduleService.getModuleAdmin(moduleId);
      if (!existingModule) {
        throw new HttpException(
          {
            success: false,
            message: 'Module not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      console.log('📁 File info:', {
        name: file.originalname,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        type: file.mimetype,
        moduleId: moduleId,
      });

      // Upload 3D model to Wasabi
      console.log('🔄 Uploading 3D model to Wasabi...');
      const uploadResult = await this.wasabiService.upload3DModel(
        file,
        moduleId,
      );
      console.log('✅ 3D model uploaded:', uploadResult.url);

      // Update module with model URL and set has3DModel flag
      const updatedModule = await this.moduleService.updateModuleAdmin(
        moduleId,
        {
          modelUrl: uploadResult.url,
          modelType: modelType || 'auto',
          has3DModel: true,
        },
      );

      console.log('✅ Module updated with 3D model URL');

      return {
        success: true,
        message: 'The 3D model has been successfully added to the module',
        data: {
          ...updatedModule,
          modelInfo: {
            url: uploadResult.url,
            key: uploadResult.key,
            size: uploadResult.size,
            contentType: uploadResult.contentType,
          },
        },
      };
    } catch (error) {
      console.error('❌ Error adding 3D model to module:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to add 3D model to module',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // ========================================
  // ADMIN CATEGORY ENDPOINTS
  // ========================================

  /**
   * [ADMIN] Create a new course
   * POST /admin/courses
   */
  @Post('courses')
  async createCourse(@Body() createCourseDto: CreateCourseDto) {
    try {
      const result =
        await this.moduleService.createCourseAdmin(createCourseDto);
      return {
        success: true,
        message: 'Course created successfully',
        data: result,
      };
    } catch (error: any) {
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to create course',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * [ADMIN] Get all courses with admin metadata
   * GET /admin/courses
   */
  @Get('courses')
  async getAllCoursesAdmin() {
    try {
      const courses = await this.moduleService.getCoursesAdmin();
      return {
        success: true,
        message: 'Courses retrieved successfully',
        data: courses,
        count: courses.length,
      };
    } catch (error: any) {
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to get courses',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * [ADMIN] Get single course by ID
   * GET /admin/courses/:id
   */
  @Get('courses/:id')
  async getCourseAdmin(@Param('id') id: string) {
    try {
      const course = await this.moduleService.getCourseById(id);
      if (!course) {
        throw new HttpException(
          {
            success: false,
            message: 'Course not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        success: true,
        message: 'Course retrieved successfully',
        data: course,
      };
    } catch (error: any) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to get course',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * [ADMIN] Update a course
   * PATCH /admin/courses/:id
   */
  @Patch('courses/:id')
  async updateCourse(
    @Param('id') id: string,
    @Body() updateCourseDto: UpdateCourseDto,
  ) {
    try {
      const result = await this.moduleService.updateCourseAdmin(
        id,
        updateCourseDto,
      );
      return {
        success: true,
        message: 'Course updated successfully',
        data: result,
      };
    } catch (error: any) {
      const statusCode = error?.message?.includes('not found')
        ? HttpStatus.NOT_FOUND
        : HttpStatus.BAD_REQUEST;

      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to update course',
          error: error?.message || 'Unknown error',
        },
        statusCode,
      );
    }
  }

  /**
   * [ADMIN] Delete a course
   * DELETE /admin/courses/:id
   */
  @Delete('courses/:id')
  async deleteCourse(@Param('id') id: string) {
    try {
      const result = await this.moduleService.deleteCourseAdmin(id);
      return {
        success: true,
        message: 'Course deleted successfully',
        data: {
          id: result.id,
          thumbnailCleanedUp: result.thumbnailCleanedUp,
          deletedModules: result.deletedModules,
        },
      };
    } catch (error: any) {
      const statusCode = error?.message?.includes('not found')
        ? HttpStatus.NOT_FOUND
        : error?.message?.includes('contains modules')
          ? HttpStatus.CONFLICT
          : HttpStatus.BAD_REQUEST;

      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to delete course',
          error: error?.message || 'Unknown error',
        },
        statusCode,
      );
    }
  }

  // ========================================
  // ADMIN STEP ENDPOINTS
  // ========================================

  /**
   * [ADMIN] Create a new step for a module
   * POST /admin/modules/:moduleId/steps
   */
  @Post('modules/:moduleId/steps')
  async createStep(
    @Param('moduleId') moduleId: string,
    @Body() createStepDto: CreateStepDto,
  ) {
    try {
      const result = await this.moduleService.createStepAdmin(
        moduleId,
        createStepDto,
      );
      return {
        success: true,
        message: 'Step created successfully',
        data: result,
      };
    } catch (error: any) {
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to create step',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * [ADMIN] Get all steps for a module
   * GET /admin/modules/:moduleId/steps
   */
  @Get('modules/:moduleId/steps')
  async getSteps(@Param('moduleId') moduleId: string) {
    try {
      const steps = await this.moduleService.getStepsAdmin(moduleId);
      return {
        success: true,
        message: 'Steps retrieved successfully',
        data: steps,
        count: steps.length,
      };
    } catch (error: any) {
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to get steps',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * [ADMIN] Get single step by ID
   * GET /admin/modules/:moduleId/steps/:stepId
   */
  @Get('modules/:moduleId/steps/:stepId')
  async getStep(
    @Param('moduleId') moduleId: string,
    @Param('stepId') stepId: string,
  ) {
    try {
      const step = await this.moduleService.getStepAdmin(moduleId, stepId);
      return {
        success: true,
        message: 'Step retrieved successfully',
        data: step,
      };
    } catch (error: any) {
      const statusCode = error?.message?.includes('not found')
        ? HttpStatus.NOT_FOUND
        : HttpStatus.INTERNAL_SERVER_ERROR;

      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to get step',
          error: error?.message || 'Unknown error',
        },
        statusCode,
      );
    }
  }

  /**
   * [ADMIN] Update a step
   * PATCH /admin/modules/:moduleId/steps/:stepId
   */
  @Patch('modules/:moduleId/steps/:stepId')
  async updateStep(
    @Param('moduleId') moduleId: string,
    @Param('stepId') stepId: string,
    @Body() updateStepDto: UpdateStepDto,
  ) {
    try {
      // Get existing step data to check for old files
      const existingStep = await this.moduleService.getStepAdmin(
        moduleId,
        stepId,
      );

      const result = await this.moduleService.updateStepAdmin(
        moduleId,
        stepId,
        updateStepDto,
      );

      // Handle file cleanup when switching from upload to URL or removing files
      const cleanupPromises: Promise<boolean>[] = [];

      // If imageUrl is being changed from Wasabi URL to external URL or being removed
      if (
        (existingStep as any)?.imageUrl &&
        (updateStepDto.imageUrl === null ||
          updateStepDto.imageUrl === undefined ||
          (updateStepDto.imageUrl &&
            !updateStepDto.imageUrl.includes('wasabisys.com')))
      ) {
        console.log(
          '🗑️ Cleaning up old image:',
          (existingStep as any).imageUrl,
        );
        cleanupPromises.push(
          this.wasabiService.deleteFileByUrl((existingStep as any).imageUrl),
        );
      }

      // If videoUrl is being changed from Wasabi URL to external URL or being removed
      if (
        (existingStep as any)?.videoUrl &&
        (updateStepDto.videoUrl === null ||
          updateStepDto.videoUrl === undefined ||
          (updateStepDto.videoUrl &&
            !updateStepDto.videoUrl.includes('wasabisys.com')))
      ) {
        console.log(
          '🗑️ Cleaning up old video:',
          (existingStep as any).videoUrl,
        );
        cleanupPromises.push(
          this.wasabiService.deleteFileByUrl((existingStep as any).videoUrl),
        );
      }

      // Execute cleanup in parallel (non-blocking)
      if (cleanupPromises.length > 0) {
        Promise.all(cleanupPromises).catch((error) => {
          console.error('⚠️ Error during file cleanup:', error);
          // Don't fail the main operation if cleanup fails
        });
      }

      return {
        success: true,
        message: 'Step updated successfully',
        data: result,
      };
    } catch (error: any) {
      const statusCode = error?.message?.includes('not found')
        ? HttpStatus.NOT_FOUND
        : HttpStatus.BAD_REQUEST;

      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to update step',
          error: error?.message || 'Unknown error',
        },
        statusCode,
      );
    }
  }

  /**
   * [ADMIN] Delete a step
   * DELETE /admin/modules/:moduleId/steps/:stepId
   */
  @Delete('modules/:moduleId/steps/:stepId')
  async deleteStep(
    @Param('moduleId') moduleId: string,
    @Param('stepId') stepId: string,
  ) {
    try {
      console.log('🚀 DELETE STEP CALLED:', { moduleId, stepId });

      const result = await this.moduleService.deleteStepAdmin(moduleId, stepId);

      console.log('🔍 DELETE RESULT:', JSON.stringify(result, null, 2));

      // Clean up files from Wasabi if they exist
      const cleanupPromises: Promise<boolean>[] = [];

      console.log('🔍 Step data for cleanup:', {
        imageUrl: result.stepData?.imageUrl,
        videoUrl: result.stepData?.videoUrl,
        stepDataExists: !!result.stepData,
      });

      if (result.stepData?.imageUrl) {
        console.log(
          '🗑️ Cleaning up deleted step image:',
          result.stepData.imageUrl,
        );
        cleanupPromises.push(
          this.wasabiService.deleteFileByUrl(result.stepData.imageUrl),
        );
      } else {
        console.log('ℹ️ No imageUrl found in stepData');
      }

      if (result.stepData?.videoUrl) {
        console.log(
          '🗑️ Cleaning up deleted step video:',
          result.stepData.videoUrl,
        );
        cleanupPromises.push(
          this.wasabiService.deleteFileByUrl(result.stepData.videoUrl),
        );
      } else {
        console.log('ℹ️ No videoUrl found in stepData');
      }

      // Execute cleanup in parallel (non-blocking)
      if (cleanupPromises.length > 0) {
        console.log(
          `🔄 Starting cleanup of ${cleanupPromises.length} files...`,
        );
        Promise.all(cleanupPromises)
          .then((results) => {
            const successCount = results.filter(Boolean).length;
            console.log(
              `✅ Cleanup completed: ${successCount}/${results.length} files deleted successfully`,
            );
          })
          .catch((error) => {
            console.error('⚠️ Error during file cleanup:', error);
            // Don't fail the main operation if cleanup fails
          });
      } else {
        console.log('ℹ️ No files to cleanup for this step');
      }

      return {
        success: true,
        message: 'Step deleted successfully',
        data: {
          id: result.id,
          moduleId: result.moduleId,
          filesCleanedUp: cleanupPromises.length,
        },
      };
    } catch (error: any) {
      console.error('❌ ERROR IN DELETE STEP:', error);
      const statusCode = error?.message?.includes('not found')
        ? HttpStatus.NOT_FOUND
        : HttpStatus.BAD_REQUEST;

      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to delete step',
          error: error?.message || 'Unknown error',
        },
        statusCode,
      );
    }
  }

  /**
   * [ADMIN] Create a new step with image upload
   * POST /admin/modules/:moduleId/steps/with-image
   */
  @Post('modules/:moduleId/steps/with-image')
  @UseInterceptors(FileInterceptor('image'))
  async createStepWithImage(
    @Param('moduleId') moduleId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('stepData') stepDataString: string,
  ) {
    try {
      console.log('🔄 Admin creating step with image...');

      // Validate file
      if (!file) {
        throw new BadRequestException('Image file is required');
      }

      // Validate file type
      if (!this.wasabiService.isValidImageFile(file)) {
        throw new BadRequestException(
          'Invalid image file format. Supported: .jpg, .jpeg, .png, .gif, .webp',
        );
      }

      // Parse step data
      let stepData: CreateStepDto;
      try {
        stepData = JSON.parse(stepDataString);
      } catch (error) {
        throw new BadRequestException('Invalid step data format');
      }

      // Validate step data
      if (!stepData.title || !stepData.content) {
        throw new BadRequestException('Step title and content are required');
      }

      console.log('📁 File info:', {
        name: file.originalname,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        type: file.mimetype,
      });

      try {
        // Upload image to Wasabi
        console.log('🔄 Uploading image to Wasabi...');
        const uploadResult = await this.wasabiService.uploadImage(
          file,
          moduleId,
        );
        console.log('✅ Image uploaded:', uploadResult.url);

        // Create step with image URL
        const stepWithImage = {
          ...stepData,
          imageUrl: uploadResult.url,
        };

        const result = await this.moduleService.createStepAdmin(
          moduleId,
          stepWithImage,
        );
        console.log('✅ Step created with image');

        return {
          success: true,
          message: 'Step created successfully with image',
          data: {
            ...result,
            imageInfo: {
              url: uploadResult.url,
              key: uploadResult.key,
              size: uploadResult.size,
              contentType: uploadResult.contentType,
            },
          },
        };
      } catch (uploadError) {
        console.error('❌ Error uploading image:', uploadError);
        throw new BadRequestException(
          `Failed to upload image: ${uploadError.message}`,
        );
      }
    } catch (error) {
      console.error('❌ Error creating step with image:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        error.message || 'Failed to create step with image',
      );
    }
  }

  /**
   * [ADMIN] Update a step with image upload
   * PATCH /admin/modules/:moduleId/steps/:stepId/with-image
   */
  @Patch('modules/:moduleId/steps/:stepId/with-image')
  @UseInterceptors(FileInterceptor('image'))
  async updateStepWithImage(
    @Param('moduleId') moduleId: string,
    @Param('stepId') stepId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('stepData') stepDataString: string,
  ) {
    try {
      console.log('🔄 Admin updating step with image...');

      // Validate file
      if (!file) {
        throw new BadRequestException('Image file is required');
      }

      // Validate file type
      if (!this.wasabiService.isValidImageFile(file)) {
        throw new BadRequestException(
          'Invalid image file format. Supported: .jpg, .jpeg, .png, .gif, .webp',
        );
      }

      // Parse step data
      let stepData: UpdateStepDto;
      try {
        stepData = JSON.parse(stepDataString);
      } catch (error) {
        throw new BadRequestException('Invalid step data format');
      }

      console.log('📁 File info:', {
        name: file.originalname,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        type: file.mimetype,
      });

      try {
        // Get existing step data to check for old image
        const existingStep = await this.moduleService.getStepAdmin(
          moduleId,
          stepId,
        );

        // Upload new image to Wasabi
        console.log('🔄 Uploading new image to Wasabi...');
        const uploadResult = await this.wasabiService.uploadImage(
          file,
          moduleId,
        );
        console.log('✅ New image uploaded:', uploadResult.url);

        // Update step with new image URL
        const stepWithImage = {
          ...stepData,
          imageUrl: uploadResult.url,
        };

        const result = await this.moduleService.updateStepAdmin(
          moduleId,
          stepId,
          stepWithImage,
        );
        console.log('✅ Step updated with new image');

        // Delete old image if it exists and is from Wasabi
        if ((existingStep as any)?.imageUrl) {
          console.log(
            '🗑️ Attempting to delete old image:',
            (existingStep as any).imageUrl,
          );
          await this.wasabiService.deleteFileByUrl(
            (existingStep as any).imageUrl,
          );
        }

        return {
          success: true,
          message: 'Step updated successfully with new image',
          data: {
            ...result,
            imageInfo: {
              url: uploadResult.url,
              key: uploadResult.key,
              size: uploadResult.size,
              contentType: uploadResult.contentType,
            },
          },
        };
      } catch (uploadError) {
        console.error('❌ Error uploading new image:', uploadError);
        throw new BadRequestException(
          `Failed to upload new image: ${uploadError.message}`,
        );
      }
    } catch (error) {
      console.error('❌ Error updating step with image:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        error.message || 'Failed to update step with image',
      );
    }
  }

  /**
   * [ADMIN] Create a new step with video upload
   * POST /admin/modules/:moduleId/steps/with-video
   */
  @Post('modules/:moduleId/steps/with-video')
  @UseInterceptors(FileInterceptor('video'))
  async createStepWithVideo(
    @Param('moduleId') moduleId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('stepData') stepDataString: string,
  ) {
    try {
      console.log('🔄 Admin creating step with video...');

      // Validate file
      if (!file) {
        throw new BadRequestException('Video file is required');
      }

      // Validate file type
      if (!this.wasabiService.isValidVideoFile(file)) {
        throw new BadRequestException(
          'Invalid video file format. Supported: .mp4, .webm, .ogg, .avi, .mov',
        );
      }

      // Parse step data
      let stepData: CreateStepDto;
      try {
        stepData = JSON.parse(stepDataString);
      } catch (error) {
        throw new BadRequestException('Invalid step data format');
      }

      // Validate step data
      if (!stepData.title || !stepData.content) {
        throw new BadRequestException('Step title and content are required');
      }

      console.log('📁 Video file info:', {
        name: file.originalname,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        type: file.mimetype,
      });

      try {
        // Upload video to Wasabi
        console.log('🔄 Uploading video to Wasabi...');
        const uploadResult = await this.wasabiService.uploadVideo(
          file,
          moduleId,
        );
        console.log('✅ Video uploaded:', uploadResult.url);

        // Create step with video URL
        const stepWithVideo = {
          ...stepData,
          videoUrl: uploadResult.url,
        };

        const result = await this.moduleService.createStepAdmin(
          moduleId,
          stepWithVideo,
        );
        console.log('✅ Step created with video');

        return {
          success: true,
          message: 'Step created successfully with video',
          data: {
            ...result,
            videoInfo: {
              url: uploadResult.url,
              key: uploadResult.key,
              size: uploadResult.size,
              contentType: uploadResult.contentType,
            },
          },
        };
      } catch (uploadError) {
        console.error('❌ Error uploading video:', uploadError);
        throw new BadRequestException(
          `Failed to upload video: ${uploadError.message}`,
        );
      }
    } catch (error) {
      console.error('❌ Error creating step with video:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        error.message || 'Failed to create step with video',
      );
    }
  }

  /**
   * [ADMIN] Update a step with video upload
   * PATCH /admin/modules/:moduleId/steps/:stepId/with-video
   */
  @Patch('modules/:moduleId/steps/:stepId/with-video')
  @UseInterceptors(FileInterceptor('video'))
  async updateStepWithVideo(
    @Param('moduleId') moduleId: string,
    @Param('stepId') stepId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('stepData') stepDataString: string,
  ) {
    try {
      console.log('🔄 Admin updating step with video...');

      // Validate file
      if (!file) {
        throw new BadRequestException('Video file is required');
      }

      // Validate file type
      if (!this.wasabiService.isValidVideoFile(file)) {
        throw new BadRequestException(
          'Invalid video file format. Supported: .mp4, .webm, .ogg, .avi, .mov',
        );
      }

      // Parse step data
      let stepData: UpdateStepDto;
      try {
        stepData = JSON.parse(stepDataString);
      } catch (error) {
        throw new BadRequestException('Invalid step data format');
      }

      console.log('📁 Video file info:', {
        name: file.originalname,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        type: file.mimetype,
      });

      try {
        // Get existing step data to check for old video
        const existingStep = await this.moduleService.getStepAdmin(
          moduleId,
          stepId,
        );

        // Upload new video to Wasabi
        console.log('🔄 Uploading new video to Wasabi...');
        const uploadResult = await this.wasabiService.uploadVideo(
          file,
          moduleId,
        );
        console.log('✅ New video uploaded:', uploadResult.url);

        // Update step with new video URL
        const stepWithVideo = {
          ...stepData,
          videoUrl: uploadResult.url,
        };

        const result = await this.moduleService.updateStepAdmin(
          moduleId,
          stepId,
          stepWithVideo,
        );
        console.log('✅ Step updated with new video');

        // Delete old video if it exists and is from Wasabi
        if ((existingStep as any)?.videoUrl) {
          console.log(
            '🗑️ Attempting to delete old video:',
            (existingStep as any).videoUrl,
          );
          await this.wasabiService.deleteFileByUrl(
            (existingStep as any).videoUrl,
          );
        }

        return {
          success: true,
          message: 'Step updated successfully with new video',
          data: {
            ...result,
            videoInfo: {
              url: uploadResult.url,
              key: uploadResult.key,
              size: uploadResult.size,
              contentType: uploadResult.contentType,
            },
          },
        };
      } catch (uploadError) {
        console.error('❌ Error uploading new video:', uploadError);
        throw new BadRequestException(
          `Failed to upload new video: ${uploadError.message}`,
        );
      }
    } catch (error) {
      console.error('❌ Error updating step with video:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        error.message || 'Failed to update step with video',
      );
    }
  }

  /**
   * [ADMIN] Update step order
   * PATCH /admin/modules/:moduleId/steps/:stepId/order
   */
  @Patch('modules/:moduleId/steps/:stepId/order')
  async updateStepOrder(
    @Param('moduleId') moduleId: string,
    @Param('stepId') stepId: string,
    @Body('order') newOrder: number,
  ) {
    try {
      console.log('🔄 Admin updating step order:', {
        moduleId,
        stepId,
        newOrder,
      });

      if (!newOrder || newOrder < 1) {
        throw new BadRequestException('Order must be a positive number');
      }

      const result = await this.moduleService.updateStepOrderAdmin(
        moduleId,
        stepId,
        newOrder,
      );

      return {
        success: true,
        message: 'Step order updated successfully',
        data: result,
      };
    } catch (error) {
      console.error('❌ Error updating step order:', error);
      throw new BadRequestException(error.message);
    }
  }

  /**
   * [ADMIN] Update quiz question order
   * PATCH /admin/modules/:moduleId/quiz/:quizId/order
   */
  @Patch('modules/:moduleId/quiz/:quizId/order')
  async updateQuizOrder(
    @Param('moduleId') moduleId: string,
    @Param('quizId') quizId: string,
    @Body('order') newOrder: number,
  ) {
    try {
      console.log('🔄 Admin updating quiz order:', {
        moduleId,
        quizId,
        newOrder,
      });

      if (!newOrder || newOrder < 1) {
        throw new BadRequestException('Order must be a positive number');
      }

      const result = await this.moduleService.updateQuizOrderAdmin(
        moduleId,
        quizId,
        newOrder,
      );

      return {
        success: true,
        message: 'Quiz order updated successfully',
        data: result,
      };
    } catch (error) {
      console.error('❌ Error updating quiz order:', error);
      throw new BadRequestException(error.message);
    }
  }

  // ========================================
  // ADMIN QUIZ ENDPOINTS
  // ========================================

  /**
   * [ADMIN] Create a new quiz question for a module
   * POST /admin/modules/:moduleId/quiz
   */
  @Post('modules/:moduleId/quiz')
  async createQuizQuestion(
    @Param('moduleId') moduleId: string,
    @Body() createQuizDto: CreateQuizQuestionDto,
  ) {
    try {
      const result = await this.moduleService.createQuizQuestionAdmin(
        moduleId,
        createQuizDto,
      );
      return {
        success: true,
        message: 'Quiz question created successfully',
        data: result,
      };
    } catch (error: any) {
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to create quiz question',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * [ADMIN] Get all quiz questions for a module
   * GET /admin/modules/:moduleId/quiz
   */
  @Get('modules/:moduleId/quiz')
  async getQuizQuestions(@Param('moduleId') moduleId: string) {
    try {
      const quizQuestions =
        await this.moduleService.getQuizQuestionsAdmin(moduleId);
      return {
        success: true,
        message: 'Quiz questions retrieved successfully',
        data: quizQuestions,
        count: quizQuestions.length,
      };
    } catch (error: any) {
      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to get quiz questions',
          error: error?.message || 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * [ADMIN] Update a quiz question
   * PATCH /admin/modules/:moduleId/quiz/:quizId
   */
  @Patch('modules/:moduleId/quiz/:quizId')
  async updateQuizQuestion(
    @Param('moduleId') moduleId: string,
    @Param('quizId') quizId: string,
    @Body() updateQuizDto: UpdateQuizQuestionDto,
  ) {
    try {
      const result = await this.moduleService.updateQuizQuestionAdmin(
        moduleId,
        quizId,
        updateQuizDto,
      );
      return {
        success: true,
        message: 'Quiz question updated successfully',
        data: result,
      };
    } catch (error: any) {
      const statusCode = error?.message?.includes('not found')
        ? HttpStatus.NOT_FOUND
        : HttpStatus.BAD_REQUEST;

      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to update quiz question',
          error: error?.message || 'Unknown error',
        },
        statusCode,
      );
    }
  }

  /**
   * [ADMIN] Delete a quiz question
   * DELETE /admin/modules/:moduleId/quiz/:quizId
   */
  @Delete('modules/:moduleId/quiz/:quizId')
  async deleteQuizQuestion(
    @Param('moduleId') moduleId: string,
    @Param('quizId') quizId: string,
  ) {
    try {
      const result = await this.moduleService.deleteQuizQuestionAdmin(
        moduleId,
        quizId,
      );
      return {
        success: true,
        message: 'Quiz question deleted successfully',
        data: result,
      };
    } catch (error: any) {
      const statusCode = error?.message?.includes('not found')
        ? HttpStatus.NOT_FOUND
        : HttpStatus.BAD_REQUEST;

      throw new HttpException(
        {
          success: false,
          message: error?.message || 'Failed to delete quiz question',
          error: error?.message || 'Unknown error',
        },
        statusCode,
      );
    }
  }

  /**
   * Upload thumbnail image for courses
   * @param file - Uploaded thumbnail file
   * @returns Upload result with URL
   */
  @Post('modules/upload-thumbnail')
  @UseInterceptors(FileInterceptor('file'))
  async uploadThumbnail(@UploadedFile() file: Express.Multer.File) {
    try {
      console.log('🔄 Admin uploading thumbnail...');

      // Validate file
      if (!file) {
        throw new BadRequestException('Thumbnail file is required');
      }

      // Validate file type
      if (!this.wasabiService.isValidImageFile(file)) {
        throw new BadRequestException(
          'Invalid image file format. Supported: .jpg, .jpeg, .png, .gif, .webp',
        );
      }

      console.log('📁 Thumbnail file info:', {
        name: file.originalname,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        type: file.mimetype,
      });

      try {
        // Upload thumbnail to Wasabi
        console.log('🔄 Uploading thumbnail to Wasabi...');
        const uploadResult = await this.wasabiService.uploadImage(
          file,
          'thumbnails', // Use 'thumbnails' as moduleId for general thumbnails
        );
        console.log('✅ Thumbnail uploaded:', uploadResult.url);

        return {
          success: true,
          message: 'Thumbnail uploaded successfully',
          data: {
            url: uploadResult.url,
            key: uploadResult.key,
            size: uploadResult.size,
            contentType: uploadResult.contentType,
          },
        };
      } catch (uploadError) {
        console.error('❌ Error uploading thumbnail:', uploadError);
        throw new BadRequestException(
          `Failed to upload thumbnail: ${uploadError.message}`,
        );
      }
    } catch (error) {
      console.error('❌ Error uploading thumbnail:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        error.message || 'Failed to upload thumbnail',
      );
    }
  }
}
