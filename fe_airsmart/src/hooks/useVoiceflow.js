import { useEffect, useRef } from 'react';

export default function VoiceflowWidget() {
    const isInitialized = useRef(false);

    useEffect(() => {
        // Only initialize widget once
        if (!isInitialized.current) {
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = "https://cdn.voiceflow.com/widget-next/bundle.mjs";
            
            script.onload = function() {
                window.voiceflow = window.voiceflow || {};
                window.voiceflow.chat.load({
                    verify: { projectID: '687f2e865a00d0facf2fc9eb' },
                    url: 'https://general-runtime.voiceflow.com',
                    versionID: 'production',
                    voice: {
                        url: "https://runtime-api.voiceflow.com"
                    }
                });
                
                isInitialized.current = true;
            };
            
            document.head.appendChild(script);
            
            // Cleanup when component unmounts
            return () => {
                if (window.voiceflow && window.voiceflow.chat && window.voiceflow.chat.destroy) {
                    try {
                        window.voiceflow.chat.destroy();
                    } catch (e) {
                        console.warn('Could not destroy voiceflow chat:', e);
                    }
                }
                
                // Remove all voiceflow elements
                const voiceflowElements = document.querySelectorAll('[data-voiceflow], .voiceflow-chat, .vf-chat');
                voiceflowElements.forEach(el => {
                    try {
                        el.remove();
                    } catch (e) {
                        console.warn('Could not remove voiceflow element:', e);
                    }
                });
                
                if (script.parentNode) {
                    try {
                        script.parentNode.removeChild(script);
                    } catch (e) {
                        console.warn('Could not remove script:', e);
                    }
                }
                isInitialized.current = false;
            };
        }
    }, []); // Empty dependency - only run once

    return null;
}