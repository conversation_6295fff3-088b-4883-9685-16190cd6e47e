body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Ensure hotspots always stay below dialogs */
.hotspot-marker {
  z-index: 100 !important;
  position: relative !important;
}

/* Ensure all dialogs stay above hotspots */
.MuiDialog-root {
  z-index: 1400 !important;
}

.MuiDrawer-root {
  z-index: 1400 !important;
}

/* Ensure Voiceflow chat widget stays above dialogs */
[data-voiceflow],
.voiceflow-chat,
.vf-chat,
.vf-widget {
  z-index: 9999 !important;
}

/* Voiceflow chat button should be above everything */
.vf-chat-button,
.voiceflow-launcher {
  z-index: 9999 !important;
}
