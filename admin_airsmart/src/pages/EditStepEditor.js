import React, { useState, useEffect, useRef, Suspense } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  AppBar,
  Toolbar,
  IconButton,
  Tabs,
  Tab,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  FormControlLabel,
  CircularProgress,
  Snackbar,
  Alert,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
  Fab,
} from '@mui/material';
import {
  ArrowBack,
  Save,
  Add as AddIcon,
  Delete as DeleteIcon,
  CameraAlt as CameraIcon,
  LocationOn as HotspotIcon,
  Gesture as ActionIcon,
  LiveHelp as TutorialIcon,
} from '@mui/icons-material';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Html } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';

import adminModuleService from '../services/adminModuleService';
import { convertToSignedUrl } from '../utils/wasabiHelper';
import * as THREE from 'three';

// Tab Panel Component for Step Dialog
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`step-tabpanel-${index}`}
      aria-labelledby={`step-tab-${index}`}
      style={{ padding: '16px 0' }}
      {...other}
    >
      {value === index && (
        <Box>{children}</Box>
      )}
    </div>
  );
}

// 3D Model Component - GLTF/GLB Only
function Model({ modelUrl, onModelClick }) {
  const meshRef = useRef();
  const [loadedModel, setLoadedModel] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    if (modelUrl) {
      setLoading(true);
      setError(null);
      setLoadedModel(null);
      
      const gltfLoader = new GLTFLoader();

      // Setup DRACO loader for compressed models
      const dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
      gltfLoader.setDRACOLoader(dracoLoader);
      
      gltfLoader.load(
        modelUrl,
        // onLoad
        (gltf) => {
          console.log('✅ GLTF model loaded successfully:', gltf);

          // Scale model to fit in view
          const box = new THREE.Box3().setFromObject(gltf.scene);
          const size = box.getSize(new THREE.Vector3()).length();
          const scale = size > 0 ? 2 / size : 1;
          gltf.scene.scale.setScalar(scale);

          // Center the model
          const center = box.getCenter(new THREE.Vector3());
          gltf.scene.position.copy(center).multiplyScalar(-scale);

          setLoadedModel(gltf);
          setLoading(false);
        },
        // onProgress
        (progress) => {
          if (progress.lengthComputable) {
            const percentComplete = (progress.loaded / progress.total) * 100;
            console.log('📊 GLTF loading progress:', percentComplete.toFixed(2) + '%');
          }
        },
        // onError
        (error) => {
          console.error('❌ Error loading GLTF model:', error);
          setError(error);
          setLoading(false);
        }
      );
    } else {
      setLoadedModel(null);
      setLoading(false);
      setError(null);
    }
  }, [modelUrl]);
  
  useFrame(() => {
    if (meshRef.current) {
      // Any animations can go here
    }
  });

  // Show loading state
  if (loading) {
    return (
      <Html center>
        <div style={{
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '12px 16px',
          borderRadius: '8px',
          fontSize: '14px',
          textAlign: 'center'
        }}>
          Loading GLTF Model...
        </div>
      </Html>
    );
  }

  // Show error state
  if (error) {
    return (
      <Html center>
        <div style={{
          background: 'rgba(255,0,0,0.8)',
          color: 'white',
          padding: '12px 16px',
          borderRadius: '8px',
          fontSize: '14px',
          textAlign: 'center',
          maxWidth: '200px'
        }}>
          <div>❌ Failed to load GLTF model</div>
          <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}>
            Check URL and CORS settings
          </div>
        </div>
      </Html>
    );
  }

  // Only render if we have a valid model
  if (!loadedModel) {
    return null;
  }

  return (
    <primitive 
      ref={meshRef}
      object={loadedModel.scene} 
      scale={[1, 1, 1]}
      onClick={onModelClick}
    />
  );
}

// Fallback component for when no model is loaded
function DefaultScene() {
  return (
    <mesh>
      <boxGeometry args={[2, 2, 2]} />
      <meshStandardMaterial color="#cccccc" />
    </mesh>
  );
}

// 3D Model Viewer Component
function ModelViewer({ 
  modelUrl, 
  hotspots = [], 
  onAddHotspot, 
  onUpdateCamera, 
  cameraPosition,
  selectedHotspot,
  onSelectHotspot,
  showHotspots = true
}) {
  const controlsRef = useRef();

  // Effect to sync camera position when cameraPosition prop changes
  useEffect(() => {
    if (controlsRef.current && cameraPosition) {
      const controls = controlsRef.current;
      const camera = controls.object;
      
      // Set camera position
      if (cameraPosition.position) {
        camera.position.set(
          cameraPosition.position[0],
          cameraPosition.position[1],
          cameraPosition.position[2]
        );
      }
      
      // Set camera target (look at)
      if (cameraPosition.target) {
        controls.target.set(
          cameraPosition.target[0],
          cameraPosition.target[1],
          cameraPosition.target[2]
        );
      }
      
      // Update the controls to apply changes
      controls.update();
    }
  }, [cameraPosition]);

  const handleModelClick = (event) => {
    if (!onAddHotspot) return;
    
    event.stopPropagation();
    
    // Convert screen coordinates to 3D world coordinates
    const intersects = event.intersections;
    if (intersects.length > 0) {
      const position = [
        intersects[0].point.x,
        intersects[0].point.y,
        intersects[0].point.z
      ];
      onAddHotspot(position);
    }
  };

  const handleCameraChange = () => {
    if (controlsRef.current && onUpdateCamera) {
      const camera = controlsRef.current.object;
      onUpdateCamera({
        position: [camera.position.x, camera.position.y, camera.position.z],
        target: [
          controlsRef.current.target.x,
          controlsRef.current.target.y,
          controlsRef.current.target.z
        ]
      });
    }
  };

  return (
    <>
      <OrbitControls 
        ref={controlsRef}
        onChange={handleCameraChange}
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        enableDamping={true}
        dampingFactor={0.05}
        rotateSpeed={1.2}
        zoomSpeed={1.2}
        panSpeed={1.0}
        autoRotate={false}
        // Cải thiện responsive và smooth movement
        minDistance={0.5}
        maxDistance={50}
        minPolarAngle={0}
        maxPolarAngle={Math.PI}
        // Cải thiện performance
        enableKeys={true}
        keyPanSpeed={7.0}
        screenSpacePanning={false}
        // Touch controls cho mobile
        touches={{
          ONE: THREE.TOUCH.ROTATE,
          TWO: THREE.TOUCH.DOLLY_PAN
        }}
        // Mouse controls optimization
        mouseButtons={{
          LEFT: THREE.MOUSE.ROTATE,
          MIDDLE: THREE.MOUSE.DOLLY,
          RIGHT: THREE.MOUSE.PAN
        }}
        // Prevent camera from going through objects
        target0={undefined}
        position0={undefined}
        zoom0={undefined}
      />
      
      {/* 3D Model or Default Scene */}
      <Suspense fallback={null}>
        {modelUrl ? (
          <Model modelUrl={modelUrl} onModelClick={handleModelClick} />
        ) : (
          <DefaultScene />
        )}
      </Suspense>
      
      {/* Hotspots */}
      {showHotspots && hotspots.map((hotspot, index) => (
        <mesh 
          key={hotspot.id || index}
          position={hotspot.position}
          onClick={(e) => {
            e.stopPropagation();
            onSelectHotspot?.(hotspot);
          }}
        >
          <sphereGeometry args={[0.1, 16, 16]} />
          <meshBasicMaterial 
            color={selectedHotspot?.id === hotspot.id ? '#ff4444' : '#44ff44'} 
          />
          <Html distanceFactor={10}>
            <div 
              style={{
                background: 'rgba(0,0,0,0.8)',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '4px',
                fontSize: '12px',
                pointerEvents: 'none'
              }}
            >
              {hotspot.label}
            </div>
          </Html>
        </mesh>
      ))}
      
      {/* Enhanced default lighting for better visibility */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 5]} intensity={1.2} castShadow />
      <directionalLight position={[-10, -10, -5]} intensity={0.8} />
      <directionalLight position={[0, 10, 10]} intensity={0.8} />
      <pointLight position={[0, 10, 0]} intensity={0.7} />
      <pointLight position={[5, 0, 5]} intensity={0.5} />
      <pointLight position={[-5, 0, -5]} intensity={0.5} />
    </>
  );
}

// Loading component for 3D canvas
function CanvasLoader() {
  return (
    <Box sx={{ 
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 2
    }}>
      <CircularProgress />
      <Typography variant="body2" color="text.secondary">
        Loading 3D Model...
      </Typography>
    </Box>
  );
}

function EditStepEditor() {
  const location = useLocation();
  const navigate = useNavigate();
  const { moduleId, stepId, moduleData, stepData: initialStepData } = location.state || {};
  
  const [loading, setLoading] = useState(false);
  const [modelUrl, setModelUrl] = useState('');
  const [tabIndex, setTabIndex] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Step data state - will be loaded from existing step
  const [stepData, setStepData] = useState({
    title: '',
    content: '',
    order: 1,
    autoPlay: false,
    tags: []
  });

  // 3D visualization state - will be loaded from existing step
  const [cameraPosition, setCameraPosition] = useState({
    position: [0, 2, 5],
    target: [0, 0, 0]
  });
  const [hotspots, setHotspots] = useState([]);
  const [selectedHotspot, setSelectedHotspot] = useState(null);

  // Default camera position constant
  const DEFAULT_CAMERA_POSITION = {
    position: [0, 2, 5],
    target: [0, 0, 0]
  };

  // Load module and step data on component mount
  useEffect(() => {
    if (!moduleId || !stepId) {
      navigate('/modules');
      return;
    }
    
    const loadData = async () => {
      setLoading(true);
      try {
        let currentModuleData = moduleData;
        if (!currentModuleData) {
          currentModuleData = await adminModuleService.getModule(moduleId);
        }
        
        // Set model URL from module data (not step data)
        if (currentModuleData && currentModuleData.modelUrl) {
          const signedUrl = convertToSignedUrl(currentModuleData.modelUrl);
          setModelUrl(signedUrl);
        }
        
        // Load step data if not provided
        let currentStepData = initialStepData;
        if (!currentStepData) {
          currentStepData = await adminModuleService.getStep(moduleId, stepId);
        }
        
        if (currentStepData) {
          setStepData({
            title: currentStepData.title || '',
            content: currentStepData.content || '',
            order: currentStepData.order || 1,
            autoPlay: currentStepData.autoPlay || false,
            tags: currentStepData.tags || []
          });
          
          // Set 3D data from step
          if (currentStepData.camera) {
            setCameraPosition(currentStepData.camera);
          }
          if (currentStepData.hotspots) {
            setHotspots(currentStepData.hotspots);
          }
        }
      } catch (error) {
        console.error('Error loading data:', error);
        setSnackbar({
          open: true,
          message: `Error loading data: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [moduleId, stepId, moduleData, initialStepData, navigate]);

  const handleUpdateStep = async () => {
    // Validate required fields
    if (!stepData.title.trim()) {
      setSnackbar({
        open: true,
        message: 'Topic title is required',
        severity: 'error'
      });
      return;
    }

    if (!stepData.content.trim()) {
      setSnackbar({
        open: true,
        message: 'Topic content is required',
        severity: 'error'
      });
      return;
    }

    // Create the complete step object
    const completeStepData = {
      ...stepData,
      camera: cameraPosition,
      hotspots: hotspots
    };
    
    setLoading(true);
    try {
      // Update the existing step
      await adminModuleService.updateStep(moduleId, stepId, completeStepData);
      setSnackbar({
        open: true,
        message: 'Topic updated successfully!',
        severity: 'success'
      });
      
      // Navigate back after a brief delay to show the success message
      setTimeout(() => {
        navigate('/modules');
      }, 1500);
    } catch (error) {
      console.error('Error updating topic:', error);
      setSnackbar({
        open: true,
        message: `Error updating topic: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddHotspot = (position) => {
    // Only allow adding hotspot when on Hotspots tab (index 1)
    if (tabIndex !== 1) {
      setSnackbar({
        open: true,
        message: 'Please switch to the "Hotspots" tab to add hotspots',
        severity: 'warning'
      });
      return;
    }

    const newHotspot = {
      id: `hotspot_${Date.now()}`,
      position,
      label: `Hotspot ${hotspots.length + 1}`,
      action: 'info',
      description: ''
    };
    setHotspots([...hotspots, newHotspot]);
    setSelectedHotspot(newHotspot);
  };

  const handleUpdateCamera = (newCamera) => {
    setCameraPosition(newCamera);
  };

  const handleResetCamera = () => {
    setCameraPosition(DEFAULT_CAMERA_POSITION);
    setSnackbar({
      open: true,
      message: 'Camera position reset to default',
      severity: 'info'
    });
  };

  if (loading) {
    return (
      <Box sx={{ 
        display: 'flex', 
        flexDirection: 'column',
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh',
        gap: 2
      }}>
        <CircularProgress size={60} />
        <Typography variant="h6" color="text.secondary">
          Loading Topic Data...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ height: 'calc(100vh - 88px)', overflow: 'hidden' }}>
      {/* Header/Toolbar */}
      <Paper sx={{ mb: 2 }} elevation={2}>
        <Toolbar>
          <IconButton edge="start" color="inherit" onClick={() => navigate('/modules')}>
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" sx={{ ml: 2, flex: 1 }}>
            Edit Topic: {stepData.title}
          </Typography>
          <Button 
            variant="contained"
            color="warning"
            startIcon={<Save />}
            onClick={handleUpdateStep}
            disabled={loading || !stepData.title || !stepData.content}
          >
            Update Topic
          </Button>
        </Toolbar>
      </Paper>

      {/* Main Content */}
      <Box sx={{ 
        display: 'flex', 
        height: 'calc(100% - 80px)', 
        gap: 1,
        px: 2
      }}>
        {/* 3D Preview Panel - Left Side */}
        <Box sx={{ 
          flex: '1 1 75%',
          height: '100%',
          minWidth: 0
        }}>
          <Paper sx={{ height: '100%', position: 'relative', overflow: 'hidden' }} elevation={3}>
            {loading && !modelUrl && <CanvasLoader />}
            
            {modelUrl && (
              <Canvas style={{ height: '100%', width: '100%' }}>
                <ModelViewer
                  modelUrl={modelUrl}
                  hotspots={hotspots}
                  onAddHotspot={handleAddHotspot}
                  onUpdateCamera={handleUpdateCamera}
                  cameraPosition={cameraPosition}
                  selectedHotspot={selectedHotspot}
                  onSelectHotspot={setSelectedHotspot}
                  showHotspots={tabIndex === 1}
                />
              </Canvas>
            )}
            
            {/* 3D Controls Toolbar */}
            <Box sx={{ 
              position: 'absolute', 
              top: 16, 
              left: 16,
              display: 'flex',
              flexDirection: 'column',
              gap: 2
            }}>
              <Tooltip title="Capture Camera Position">
                <Fab size="small" color="primary" onClick={() => {
                  setSnackbar({
                    open: true, 
                    message: 'Camera position captured', 
                    severity: 'info'
                  });
                }}>
                  <CameraIcon />
                </Fab>
              </Tooltip>
              <Tooltip title="Add Hotspot (Click on model)">
                <Fab size="small" color="secondary">
                  <HotspotIcon />
                </Fab>
              </Tooltip>
              <Tooltip title="Reset Camera to Default">
                <Fab size="small" color="default" onClick={handleResetCamera}>
                  <ArrowBack />
                </Fab>
              </Tooltip>
            </Box>
            
            {/* Camera Position Info */}
            <Box sx={{ 
              position: 'absolute', 
              bottom: 16, 
              left: 16, 
              right: 16,
              background: 'rgba(0,0,0,0.5)',
              borderRadius: 1,
              p: 1,
              color: 'white',
              fontSize: '12px'
            }}>
              <Typography variant="caption" sx={{ color: 'white', display: 'block' }}>
                Camera Pos: [{cameraPosition.position.map(p => Number(p).toFixed(2)).join(', ')}]
              </Typography>
              <Typography variant="caption" sx={{ color: 'white', display: 'block' }}>
                Look At: [{cameraPosition.target.map(p => Number(p).toFixed(2)).join(', ')}]
              </Typography>
            </Box>
          </Paper>
        </Box>

        {/* Step Editor Panel - Right Side */}
        <Box sx={{ 
          flex: '1 1 25%',
          height: '100%',
          minWidth: '320px',
          maxWidth: '400px'
        }}>
          <Paper sx={{ height: '100%', overflow: 'hidden', display: 'flex', flexDirection: 'column' }} elevation={3}>
            {/* Tabs Navigation */}
            <AppBar position="static" color="default" elevation={0} sx={{ flexShrink: 0 }}>
              <Tabs
                value={tabIndex}
                onChange={(e, newValue) => setTabIndex(newValue)}
                indicatorColor="primary"
                textColor="primary"
                variant="fullWidth"
                sx={{
                  '& .MuiTab-root': {
                    minHeight: '48px',
                    fontSize: '0.75rem',
                    padding: '6px 4px'
                  }
                }}
              >
                <Tab label="Basic" icon={<CameraIcon fontSize="small" />} iconPosition="top" />
                <Tab label="Hotspots" icon={<HotspotIcon fontSize="small" />} iconPosition="top" />
              </Tabs>
            </AppBar>

            {/* Tab Content - Scrollable */}
            <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
              {/* Tab 1: Basic Information */}
              <TabPanel value={tabIndex} index={0}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                  <TextField
                    label="Topic Title"
                    value={stepData.title}
                    onChange={(e) => setStepData({ ...stepData, title: e.target.value })}
                    fullWidth
                    required
                    size="small"
                  />
                  
                  <TextField
                    label="Content"
                    value={stepData.content}
                    onChange={(e) => setStepData({ ...stepData, content: e.target.value })}
                    multiline
                    rows={3}
                    fullWidth
                    required
                    placeholder="Detailed topic instructions. This will be displayed to the user."
                    size="small"
                  />

                  <Grid container spacing={1}>
                    <Grid item xs={6}>
                      <TextField
                        label="Order"
                        type="number"
                        value={stepData.order}
                        onChange={(e) => setStepData({ ...stepData, order: parseInt(e.target.value) || 1 })}
                        fullWidth
                        size="small"
                        InputProps={{
                          startAdornment: <InputAdornment position="start">#</InputAdornment>,
                        }}
                      />
                    </Grid>
                  </Grid>

                  <TextField
                    label="Tags (comma separated)"
                    value={stepData.tags ? stepData.tags.join(', ') : ''}
                    onChange={(e) => setStepData({
                      ...stepData,
                      tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                    })}
                    fullWidth
                    placeholder="e.g. safety, installation, important"
                    size="small"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={stepData.autoPlay || false}
                        onChange={(e) => setStepData({ ...stepData, autoPlay: e.target.checked })}
                        size="small"
                      />
                    }
                    label="Auto-play actions when topic is loaded"
                  />
                  
                  <Divider sx={{ my: 1 }} />
                  
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>Camera Position</Typography>
                  
                  <Alert severity="info" sx={{ mb: 1, p: 1 }}>
                    <Typography variant="caption">
                      Navigate the 3D model to set the desired camera position. The position is automatically updated.
                    </Typography>
                  </Alert>
                  
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Typography variant="caption" sx={{ fontWeight: 500 }}>Position (x, y, z)</Typography>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <TextField
                        label="X"
                        value={Number(cameraPosition.position[0]).toFixed(2)}
                        onChange={(e) => {
                          const newPosition = [...cameraPosition.position];
                          newPosition[0] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            position: newPosition
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                      <TextField
                        label="Y"
                        value={Number(cameraPosition.position[1]).toFixed(2)}
                        onChange={(e) => {
                          const newPosition = [...cameraPosition.position];
                          newPosition[1] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            position: newPosition
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                      <TextField
                        label="Z"
                        value={Number(cameraPosition.position[2]).toFixed(2)}
                        onChange={(e) => {
                          const newPosition = [...cameraPosition.position];
                          newPosition[2] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            position: newPosition
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                    </Box>

                    <Typography variant="caption" sx={{ fontWeight: 500 }}>Target / Look At (x, y, z)</Typography>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <TextField
                        label="X"
                        value={Number(cameraPosition.target[0]).toFixed(2)}
                        onChange={(e) => {
                          const newTarget = [...cameraPosition.target];
                          newTarget[0] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            target: newTarget
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                      <TextField
                        label="Y"
                        value={Number(cameraPosition.target[1]).toFixed(2)}
                        onChange={(e) => {
                          const newTarget = [...cameraPosition.target];
                          newTarget[1] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            target: newTarget
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                      <TextField
                        label="Z"
                        value={Number(cameraPosition.target[2]).toFixed(2)}
                        onChange={(e) => {
                          const newTarget = [...cameraPosition.target];
                          newTarget[2] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            target: newTarget
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                    </Box>

                    <Button
                      variant="outlined"
                      size="small"
                      onClick={handleResetCamera}
                      sx={{ mt: 1 }}
                    >
                      Reset to Default
                    </Button>
                  </Box>
                </Box>
              </TabPanel>
              
              {/* Tab 2: Hotspots */}
              <TabPanel value={tabIndex} index={1}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6">
                      Hotspots ({hotspots.length})
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={() => {
                        setSnackbar({
                          open: true,
                          message: 'Click on the 3D model to place a hotspot',
                          severity: 'info'
                        });
                      }}
                    >
                      Add Hotspot
                    </Button>
                  </Box>
                  
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Click on the 3D model to add hotspots. Click on a hotspot to select and edit it.
                  </Alert>
                  
                  <List>
                    {hotspots.map((hotspot, index) => (
                      <ListItem 
                        key={hotspot.id || index}
                        button
                        selected={selectedHotspot?.id === hotspot.id}
                        onClick={() => setSelectedHotspot(hotspot)}
                      >
                        <ListItemText 
                          primary={hotspot.label}
                          secondary={`Position: [${hotspot.position.map(p => Number(p).toFixed(1)).join(', ')}]`}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            onClick={() => {
                              setHotspots(hotspots.filter(h => h.id !== hotspot.id));
                              if (selectedHotspot?.id === hotspot.id) {
                                setSelectedHotspot(null);
                              }
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                  
                  {hotspots.length === 0 && (
                    <Box sx={{ textAlign: 'center', p: 3, bgcolor: 'background.paper' }}>
                      <Typography color="text.secondary">
                        No hotspots added yet. Click on the 3D model to add one.
                      </Typography>
                    </Box>
                  )}
                  
                  {selectedHotspot && (
                    <Card variant="outlined" sx={{ mt: 2 }}>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Edit Selected Hotspot
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
                          <TextField
                            label="Label"
                            value={selectedHotspot.label}
                            onChange={(e) => {
                              const updatedHotspots = hotspots.map(h => 
                                h.id === selectedHotspot.id 
                                  ? { ...h, label: e.target.value } 
                                  : h
                              );
                              setHotspots(updatedHotspots);
                              setSelectedHotspot({ ...selectedHotspot, label: e.target.value });
                            }}
                            fullWidth
                            size="small"
                          />
                          
                          <FormControl fullWidth size="small">
                            <InputLabel>Action Type</InputLabel>
                            <Select
                              value={selectedHotspot.action || 'info'}
                              label="Action Type"
                              onChange={(e) => {
                                const updatedHotspots = hotspots.map(h => 
                                  h.id === selectedHotspot.id 
                                    ? { ...h, action: e.target.value } 
                                    : h
                                );
                                setHotspots(updatedHotspots);
                                setSelectedHotspot({ ...selectedHotspot, action: e.target.value });
                              }}
                            >
                              <MenuItem value="info">Information</MenuItem>
                              <MenuItem value="highlight">Highlight</MenuItem>
                              <MenuItem value="popup">Popup</MenuItem>
                              <MenuItem value="animate">Animate</MenuItem>
                            </Select>
                          </FormControl>
                          
                          <TextField
                            label="Description"
                            value={selectedHotspot.description || ''}
                            onChange={(e) => {
                              const updatedHotspots = hotspots.map(h => 
                                h.id === selectedHotspot.id 
                                  ? { ...h, description: e.target.value } 
                                  : h
                              );
                              setHotspots(updatedHotspots);
                              setSelectedHotspot({ ...selectedHotspot, description: e.target.value });
                            }}
                            multiline
                            rows={3}
                            fullWidth
                            size="small"
                          />
                          
                          <Typography variant="subtitle2">Position (x, y, z)</Typography>
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <TextField
                              label="X"
                              type="number"
                              inputProps={{ step: 0.1 }}
                              value={Number(selectedHotspot.position[0]).toFixed(2)}
                              onChange={(e) => {
                                const newPosition = [...selectedHotspot.position];
                                newPosition[0] = parseFloat(e.target.value) || 0;
                                
                                const updatedHotspots = hotspots.map(h => 
                                  h.id === selectedHotspot.id 
                                    ? { ...h, position: newPosition } 
                                    : h
                                );
                                setHotspots(updatedHotspots);
                                setSelectedHotspot({ ...selectedHotspot, position: newPosition });
                              }}
                              fullWidth
                              size="small"
                            />
                            <TextField
                              label="Y"
                              type="number"
                              inputProps={{ step: 0.1 }}
                              value={Number(selectedHotspot.position[1]).toFixed(2)}
                              onChange={(e) => {
                                const newPosition = [...selectedHotspot.position];
                                newPosition[1] = parseFloat(e.target.value) || 0;
                                
                                const updatedHotspots = hotspots.map(h => 
                                  h.id === selectedHotspot.id 
                                    ? { ...h, position: newPosition } 
                                    : h
                                );
                                setHotspots(updatedHotspots);
                                setSelectedHotspot({ ...selectedHotspot, position: newPosition });
                              }}
                              fullWidth
                              size="small"
                            />
                            <TextField
                              label="Z"
                              type="number"
                              inputProps={{ step: 0.1 }}
                              value={Number(selectedHotspot.position[2]).toFixed(2)}
                              onChange={(e) => {
                                const newPosition = [...selectedHotspot.position];
                                newPosition[2] = parseFloat(e.target.value) || 0;
                                
                                const updatedHotspots = hotspots.map(h => 
                                  h.id === selectedHotspot.id 
                                    ? { ...h, position: newPosition } 
                                    : h
                                );
                                setHotspots(updatedHotspots);
                                setSelectedHotspot({ ...selectedHotspot, position: newPosition });
                              }}
                              fullWidth
                              size="small"
                            />
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  )}
                </Box>
              </TabPanel>
            </Box>
          </Paper>
        </Box>
      </Box>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default EditStepEditor;